# دليل التثبيت والتشغيل - نظام SBEA

## 📋 المتطلبات الأساسية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.15+, أو Linux Ubuntu 18.04+
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **مساحة القرص**: 2 GB مساحة فارغة
- **الاتصال**: اتصال إنترنت للتحديثات والتكامل مع WhatsApp

### البرامج المطلوبة
1. **Node.js** (الإصدار 18 أو أحدث)
   - تحميل من: https://nodejs.org/
   - تأكد من تثبيت npm معه

2. **PostgreSQL** (الإصدار 13 أو أحدث)
   - تحميل من: https://www.postgresql.org/download/
   - أو استخدم خدمة سحابية مثل Supabase

3. **Git** (اختياري للتطوير)
   - تحميل من: https://git-scm.com/

## 🚀 خطوات التثبيت

### الطريقة السريعة (Windows)
1. تحميل ملفات المشروع
2. فتح مجلد المشروع
3. النقر المزدوج على `start.bat`
4. انتظار اكتمال التثبيت والتشغيل

### الطريقة السريعة (Mac/Linux)
1. تحميل ملفات المشروع
2. فتح Terminal في مجلد المشروع
3. تشغيل الأمر: `chmod +x start.sh && ./start.sh`
4. انتظار اكتمال التثبيت والتشغيل

### التثبيت اليدوي

#### 1. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة بيانات جديدة
createdb sbea_db

# أو استخدام PostgreSQL CLI
psql -U postgres
CREATE DATABASE sbea_db;
\q
```

#### 2. إعداد متغيرات البيئة
```bash
# نسخ ملف البيئة
cp .env.local.example .env.local

# تحرير الملف وإدخال بياناتك
nano .env.local
```

#### 3. تثبيت التبعيات
```bash
# تثبيت حزم Node.js
npm install

# أو استخدام Yarn
yarn install
```

#### 4. إعداد قاعدة البيانات
```bash
# تشغيل migrations
npx prisma migrate dev

# توليد Prisma Client
npx prisma generate

# (اختياري) إدخال بيانات تجريبية
npx prisma db seed
```

#### 5. تشغيل المشروع
```bash
# تشغيل في وضع التطوير
npm run dev

# أو تشغيل في وضع الإنتاج
npm run build
npm start
```

## 🔧 الإعدادات المتقدمة

### إعداد WhatsApp Business API
1. إنشاء حساب Facebook Developer
2. إنشاء تطبيق WhatsApp Business
3. الحصول على:
   - Phone Number ID
   - Access Token
   - Webhook URL
4. إدخال البيانات في `.env.local`:
```env
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
```

### إعداد البريد الإلكتروني (اختياري)
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### إعداد رفع الملفات
```env
MAX_FILE_SIZE=5242880
UPLOAD_DIR=./uploads
```

## 🌐 الوصول للنظام

### الروابط الأساسية
- **الصفحة الرئيسية**: http://localhost:3000
- **تسجيل الدخول**: http://localhost:3000/auth/login
- **لوحة التحكم**: http://localhost:3000/dashboard

### بيانات تسجيل الدخول التجريبية
```
المدير العام:
اسم المستخدم: admin
كلمة المرور: admin123

معلم:
اسم المستخدم: teacher1
كلمة المرور: teacher123

موظف:
اسم المستخدم: employee1
كلمة المرور: employee123
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```bash
# تحقق من تشغيل PostgreSQL
sudo systemctl status postgresql

# تحقق من صحة رابط قاعدة البيانات
psql "postgresql://username:password@localhost:5432/sbea_db"
```

#### خطأ في تثبيت التبعيات
```bash
# مسح cache npm
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

#### خطأ في المنافذ
```bash
# تغيير المنفذ في package.json
"dev": "next dev -p 3001"

# أو تعيين متغير البيئة
PORT=3001 npm run dev
```

#### مشاكل في Prisma
```bash
# إعادة توليد Prisma Client
npx prisma generate

# إعادة تعيين قاعدة البيانات
npx prisma migrate reset
```

## 📱 إعداد الهاتف المحمول

### متطلبات الوصول عبر الهاتف
1. تأكد من أن الجهاز والهاتف على نفس الشبكة
2. استخدم عنوان IP الجهاز بدلاً من localhost
3. مثال: http://*************:3000

### العثور على عنوان IP
```bash
# Windows
ipconfig

# Mac/Linux
ifconfig
# أو
ip addr show
```

## 🔒 الأمان والحماية

### إعدادات الأمان الأساسية
1. تغيير كلمات المرور الافتراضية
2. تفعيل HTTPS في الإنتاج
3. إعداد جدار الحماية
4. تحديث النظام بانتظام

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
pg_dump sbea_db > backup_$(date +%Y%m%d).sql

# استعادة النسخة الاحتياطية
psql sbea_db < backup_20240101.sql
```

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **التوثيق**: راجع ملف README.md
- **المشاكل التقنية**: إنشاء Issue في GitHub
- **الدعم المباشر**: التواصل مع فريق التطوير

### معلومات مفيدة للدعم
عند طلب المساعدة، يرجى تقديم:
1. نظام التشغيل والإصدار
2. إصدار Node.js (`node --version`)
3. رسالة الخطأ كاملة
4. خطوات إعادة إنتاج المشكلة

## 🔄 التحديثات

### تحديث النظام
```bash
# تحديث التبعيات
npm update

# تحديث قاعدة البيانات
npx prisma migrate deploy

# إعادة تشغيل النظام
npm restart
```

### متابعة التحديثات
- تحقق من الإصدارات الجديدة بانتظام
- اقرأ ملاحظات الإصدار قبل التحديث
- اعمل نسخة احتياطية قبل التحديث

---

**ملاحظة**: هذا الدليل يغطي الإعدادات الأساسية. للإعدادات المتقدمة أو البيئات الإنتاجية، يرجى مراجعة التوثيق التقني المفصل.
