{"name": "sbea-system", "version": "1.0.0", "description": "نظام إدارة مدرسي متكامل لمؤسسة النور التربوي", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "prisma": "^5.0.0", "@prisma/client": "^5.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "next-auth": "^4.24.0", "react-hook-form": "^7.45.0", "react-query": "^3.39.0", "axios": "^1.5.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-barcode": "^1.4.6", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "react-hot-toast": "^2.4.1", "lucide-react": "^0.263.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2"}}