// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String
  email     String?  @unique
  role      UserRole @default(TEACHER)
  fullName  String
  phone     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  teacher Teacher?

  @@map("users")
}

model Student {
  id              String   @id @default(cuid())
  studentCode     String   @unique
  barcode         String   @unique
  firstName       String
  lastName        String
  birthDate       DateTime?
  gender          Gender
  address         String?
  parentPhone     String
  parentEmail     String?
  parentName      String
  guardianName    String?
  guardianPhone   String?
  levelId         String
  feesAmount      Decimal  @default(0)
  paidAmount      Decimal  @default(0)
  remainingAmount Decimal  @default(0)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  level         Level           @relation(fields: [levelId], references: [id])
  studentGroups StudentGroup[]
  payments      Payment[]
  attendance    Attendance[]

  @@map("students")
}

model Teacher {
  id            String        @id @default(cuid())
  teacherCode   String        @unique
  firstName     String
  lastName      String
  email         String?       @unique
  phone         String
  address       String?
  specialization String?
  hourlyRate    Decimal       @default(0)
  paymentMethod PaymentMethod @default(HOURLY)
  isActive      Boolean       @default(true)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  user           User?            @relation(fields: [userId], references: [id])
  userId         String?          @unique
  groups         Group[]
  teacherPayments TeacherPayment[]

  @@map("teachers")
}

model Level {
  id          String    @id @default(cuid())
  levelName   String
  levelType   LevelType
  levelNumber Int
  description String?
  isActive    Boolean   @default(true)

  // Relations
  students Student[]
  subjects Subject[]
  groups   Group[]

  @@map("levels")
}

model Subject {
  id          String  @id @default(cuid())
  subjectName String
  subjectCode String  @unique
  levelId     String
  description String?
  isActive    Boolean @default(true)

  // Relations
  level      Level               @relation(fields: [levelId], references: [id])
  components SubjectComponent[]
  groups     Group[]

  @@map("subjects")
}

model SubjectComponent {
  id            String  @id @default(cuid())
  componentName String
  subjectId     String
  description   String?
  isActive      Boolean @default(true)

  // Relations
  subject Subject @relation(fields: [subjectId], references: [id])

  @@map("subject_components")
}

model Group {
  id           String   @id @default(cuid())
  groupName    String
  levelId      String
  teacherId    String
  subjectId    String
  maxStudents  Int      @default(30)
  groupFee     Decimal  @default(0)
  scheduleDays String[] // ["MONDAY", "TUESDAY", "THURSDAY", "FRIDAY"]
  morningTime  String?  // "08:00-12:00"
  eveningTime  String?  // "14:00-18:00"
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())

  // Relations
  level         Level           @relation(fields: [levelId], references: [id])
  teacher       Teacher         @relation(fields: [teacherId], references: [id])
  subject       Subject         @relation(fields: [subjectId], references: [id])
  studentGroups StudentGroup[]
  attendance    Attendance[]
  schedule      Schedule[]

  @@map("groups")
}

model StudentGroup {
  id             String   @id @default(cuid())
  studentId      String
  groupId        String
  enrollmentDate DateTime @default(now())
  isActive       Boolean  @default(true)

  // Relations
  student Student @relation(fields: [studentId], references: [id])
  group   Group   @relation(fields: [groupId], references: [id])

  @@unique([studentId, groupId])
  @@map("student_groups")
}

model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Decimal
  paymentType   PaymentType
  paymentMethod String        @default("CASH")
  paymentDate   DateTime      @default(now())
  description   String?
  receiptNumber String?       @unique
  createdAt     DateTime      @default(now())

  // Relations
  student Student @relation(fields: [studentId], references: [id])

  @@map("payments")
}

model TeacherPayment {
  id          String      @id @default(cuid())
  teacherId   String
  amount      Decimal
  paymentType PaymentType
  paymentDate DateTime    @default(now())
  description String?
  createdAt   DateTime    @default(now())

  // Relations
  teacher Teacher @relation(fields: [teacherId], references: [id])

  @@map("teacher_payments")
}

model Attendance {
  id             String      @id @default(cuid())
  studentId      String
  groupId        String
  attendanceDate DateTime    @default(now())
  sessionType    SessionType
  isPresent      Boolean     @default(false)
  notes          String?
  createdAt      DateTime    @default(now())

  // Relations
  student Student @relation(fields: [studentId], references: [id])
  group   Group   @relation(fields: [groupId], references: [id])

  @@unique([studentId, groupId, attendanceDate, sessionType])
  @@map("attendance")
}

model Schedule {
  id          String      @id @default(cuid())
  groupId     String
  dayOfWeek   DayOfWeek
  sessionType SessionType
  startTime   String      // "08:00"
  endTime     String      // "10:00"
  isActive    Boolean     @default(true)

  // Relations
  group Group @relation(fields: [groupId], references: [id])

  @@map("schedule")
}

model Notification {
  id               String           @id @default(cuid())
  title            String
  message          String
  recipientType    RecipientType
  recipientId      String?
  notificationType NotificationType
  isSent           Boolean          @default(false)
  sentAt           DateTime?
  createdAt        DateTime         @default(now())

  @@map("notifications")
}

// Enums
enum UserRole {
  ADMIN
  TEACHER
  EMPLOYEE
}

enum Gender {
  MALE
  FEMALE
}

enum LevelType {
  FIRST_LEVEL    // المستوى الأول
  PRIMARY        // الابتدائي
  MIDDLE         // الإعدادي
}

enum PaymentMethod {
  HOURLY         // حسب الساعة
  MONTHLY        // شهري
  PER_STUDENT    // حسب عدد التلاميذ
  FIXED          // ثابت
  PERCENTAGE     // نسبة مئوية
}

enum PaymentType {
  TUITION        // رسوم دراسية
  REGISTRATION   // رسوم تسجيل
  BOOK           // كتب
  ACTIVITY       // أنشطة
  OTHER          // أخرى
  SALARY         // راتب
  BONUS          // مكافأة
}

enum SessionType {
  MORNING        // صباحي
  EVENING        // مسائي
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

enum RecipientType {
  ALL
  STUDENTS
  TEACHERS
  PARENTS
  SPECIFIC
}

enum NotificationType {
  PAYMENT_DUE    // استحقاق دفعة
  PAYMENT_RECEIVED // استلام دفعة
  ATTENDANCE     // حضور
  GENERAL        // عام
  WHATSAPP       // واتساب
}
