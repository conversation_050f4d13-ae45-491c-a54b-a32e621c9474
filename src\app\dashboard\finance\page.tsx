'use client'

import { useState, useEffect } from 'react'
import { 
  DollarSign, 
  Plus, 
  Search, 
  Filter, 
  Download,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle,
  Clock,
  Receipt,
  CreditCard,
  Banknote
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface Payment {
  id: string
  studentName: string
  studentCode: string
  amount: number
  paymentType: 'TUITION' | 'REGISTRATION' | 'BOOK' | 'ACTIVITY' | 'OTHER'
  paymentMethod: string
  paymentDate: string
  receiptNumber: string
  status: 'PAID' | 'PENDING' | 'OVERDUE'
}

interface FinancialSummary {
  totalRevenue: number
  monthlyRevenue: number
  pendingPayments: number
  overduePayments: number
  totalExpenses: number
  netProfit: number
  revenueGrowth: number
  expenseGrowth: number
}

export default function FinancePage() {
  const [payments, setPayments] = useState<Payment[]>([])
  const [summary, setSummary] = useState<FinancialSummary>({
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingPayments: 0,
    overduePayments: 0,
    totalExpenses: 0,
    netProfit: 0,
    revenueGrowth: 0,
    expenseGrowth: 0
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPaymentType, setSelectedPaymentType] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    const mockPayments: Payment[] = [
      {
        id: '1',
        studentName: 'أحمد محمد',
        studentCode: 'STD001',
        amount: 8000,
        paymentType: 'TUITION',
        paymentMethod: 'نقدي',
        paymentDate: '2024-01-15',
        receiptNumber: 'RCP001',
        status: 'PAID'
      },
      {
        id: '2',
        studentName: 'فاطمة علي',
        studentCode: 'STD002',
        amount: 7500,
        paymentType: 'TUITION',
        paymentMethod: 'تحويل بنكي',
        paymentDate: '2024-01-14',
        receiptNumber: 'RCP002',
        status: 'PAID'
      },
      {
        id: '3',
        studentName: 'خالد حسن',
        studentCode: 'STD003',
        amount: 4500,
        paymentType: 'TUITION',
        paymentMethod: 'نقدي',
        paymentDate: '2024-01-10',
        receiptNumber: 'RCP003',
        status: 'PENDING'
      },
      {
        id: '4',
        studentName: 'مريم أحمد',
        studentCode: 'STD004',
        amount: 2000,
        paymentType: 'REGISTRATION',
        paymentMethod: 'نقدي',
        paymentDate: '2024-01-05',
        receiptNumber: 'RCP004',
        status: 'OVERDUE'
      }
    ]

    const mockSummary: FinancialSummary = {
      totalRevenue: 485000,
      monthlyRevenue: 125000,
      pendingPayments: 15,
      overduePayments: 8,
      totalExpenses: 320000,
      netProfit: 165000,
      revenueGrowth: 12.5,
      expenseGrowth: 8.2
    }
    
    setTimeout(() => {
      setPayments(mockPayments)
      setSummary(mockSummary)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = 
      payment.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.studentCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.receiptNumber.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesPaymentType = selectedPaymentType === '' || payment.paymentType === selectedPaymentType
    const matchesStatus = selectedStatus === '' || payment.status === selectedStatus
    
    return matchesSearch && matchesPaymentType && matchesStatus
  })

  const getPaymentTypeText = (type: string) => {
    const types = {
      TUITION: 'رسوم دراسية',
      REGISTRATION: 'رسوم تسجيل',
      BOOK: 'كتب',
      ACTIVITY: 'أنشطة',
      OTHER: 'أخرى'
    }
    return types[type as keyof typeof types] || type
  }

  const getStatusColor = (status: string) => {
    const colors = {
      PAID: 'bg-green-100 text-green-800',
      PENDING: 'bg-yellow-100 text-yellow-800',
      OVERDUE: 'bg-red-100 text-red-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getStatusText = (status: string) => {
    const statuses = {
      PAID: 'مدفوع',
      PENDING: 'معلق',
      OVERDUE: 'متأخر'
    }
    return statuses[status as keyof typeof statuses] || status
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">النظام المالي</h1>
            <p className="text-gray-600">إدارة الرسوم والمدفوعات والتقارير المالية</p>
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              <Download className="w-4 h-4 ml-2" />
              تصدير التقرير
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 ml-2" />
              تسجيل دفعة جديدة
            </Button>
          </div>
        </div>

        {/* Financial Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">الإيرادات الشهرية</p>
                  <p className="text-2xl font-bold text-green-600">
                    {summary.monthlyRevenue.toLocaleString()} دج
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                    <span className="text-sm text-green-600">+{summary.revenueGrowth}%</span>
                  </div>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المدفوعات المعلقة</p>
                  <p className="text-2xl font-bold text-yellow-600">{summary.pendingPayments}</p>
                  <p className="text-sm text-gray-500 mt-1">دفعة معلقة</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المدفوعات المتأخرة</p>
                  <p className="text-2xl font-bold text-red-600">{summary.overduePayments}</p>
                  <p className="text-sm text-gray-500 mt-1">دفعة متأخرة</p>
                </div>
                <AlertCircle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">صافي الربح</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {summary.netProfit.toLocaleString()} دج
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-blue-500 ml-1" />
                    <span className="text-sm text-blue-600">+15.2%</span>
                  </div>
                </div>
                <TrendingUp className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات سريعة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="secondary" className="h-20 flex-col">
                <Receipt className="w-6 h-6 mb-2" />
                <span className="text-sm">إنشاء فاتورة</span>
              </Button>
              <Button variant="secondary" className="h-20 flex-col">
                <CreditCard className="w-6 h-6 mb-2" />
                <span className="text-sm">تسجيل دفعة</span>
              </Button>
              <Button variant="secondary" className="h-20 flex-col">
                <Banknote className="w-6 h-6 mb-2" />
                <span className="text-sm">دفع أجور</span>
              </Button>
              <Button variant="secondary" className="h-20 flex-col">
                <Download className="w-6 h-6 mb-2" />
                <span className="text-sm">تقرير مالي</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                placeholder="البحث بالاسم أو رقم الإيصال..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
              />
              
              <select
                value={selectedPaymentType}
                onChange={(e) => setSelectedPaymentType(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">نوع الدفعة</option>
                <option value="TUITION">رسوم دراسية</option>
                <option value="REGISTRATION">رسوم تسجيل</option>
                <option value="BOOK">كتب</option>
                <option value="ACTIVITY">أنشطة</option>
                <option value="OTHER">أخرى</option>
              </select>
              
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">حالة الدفع</option>
                <option value="PAID">مدفوع</option>
                <option value="PENDING">معلق</option>
                <option value="OVERDUE">متأخر</option>
              </select>
              
              <Button variant="secondary" className="w-full">
                <Filter className="w-4 h-4 ml-2" />
                تصفية متقدمة
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Payments Table */}
        <Card>
          <CardHeader>
            <CardTitle>المدفوعات الأخيرة ({filteredPayments.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التلميذ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        نوع الدفعة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المبلغ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        طريقة الدفع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الدفع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        رقم الإيصال
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredPayments.map((payment) => (
                      <tr key={payment.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {payment.studentName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {payment.studentCode}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {getPaymentTypeText(payment.paymentType)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {payment.amount.toLocaleString()} دج
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {payment.paymentMethod}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(payment.paymentDate).toLocaleDateString('ar-DZ')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                            {getStatusText(payment.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {payment.receiptNumber}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
