'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Users, 
  GraduationCap, 
  Calculator, 
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import DashboardLayout from '@/components/layout/DashboardLayout'

interface DashboardStats {
  totalStudents: number
  totalTeachers: number
  totalRevenue: number
  pendingPayments: number
  todayClasses: number
  presentStudents: number
}

export default function DashboardPage() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 245,
    totalTeachers: 18,
    totalRevenue: 125000,
    pendingPayments: 15,
    todayClasses: 12,
    presentStudents: 198
  })

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user')
    if (!userData) {
      router.push('/auth/login')
      return
    }
    setUser(JSON.parse(userData))
  }, [router])

  if (!user) {
    return <div>جاري التحميل...</div>
  }

  const statCards = [
    {
      title: 'إجمالي التلاميذ',
      value: stats.totalStudents,
      icon: <Users className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'إجمالي المعلمين',
      value: stats.totalTeachers,
      icon: <GraduationCap className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+2',
      changeType: 'positive'
    },
    {
      title: 'الإيرادات الشهرية',
      value: `${stats.totalRevenue.toLocaleString()} دج`,
      icon: <DollarSign className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+8%',
      changeType: 'positive'
    },
    {
      title: 'المدفوعات المعلقة',
      value: stats.pendingPayments,
      icon: <AlertCircle className="w-6 h-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: '-3',
      changeType: 'negative'
    }
  ]

  const todayStats = [
    {
      title: 'حصص اليوم',
      value: stats.todayClasses,
      icon: <Calendar className="w-5 h-5" />,
      color: 'text-indigo-600'
    },
    {
      title: 'التلاميذ الحاضرون',
      value: stats.presentStudents,
      icon: <CheckCircle className="w-5 h-5" />,
      color: 'text-green-600'
    },
    {
      title: 'معدل الحضور',
      value: `${Math.round((stats.presentStudents / stats.totalStudents) * 100)}%`,
      icon: <TrendingUp className="w-5 h-5" />,
      color: 'text-blue-600'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'payment',
      message: 'تم استلام دفعة من التلميذ أحمد محمد',
      amount: '5000 دج',
      time: 'منذ 10 دقائق',
      icon: <DollarSign className="w-4 h-4 text-green-600" />
    },
    {
      id: 2,
      type: 'enrollment',
      message: 'تسجيل تلميذ جديد: فاطمة علي',
      time: 'منذ 30 دقيقة',
      icon: <Users className="w-4 h-4 text-blue-600" />
    },
    {
      id: 3,
      type: 'class',
      message: 'بدء حصة الرياضيات للمستوى الثالث',
      time: 'منذ ساعة',
      icon: <Calendar className="w-4 h-4 text-purple-600" />
    },
    {
      id: 4,
      type: 'alert',
      message: 'تذكير: استحقاق راتب المعلم محمد أحمد',
      time: 'منذ ساعتين',
      icon: <AlertCircle className="w-4 h-4 text-orange-600" />
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            مرحباً بك، {user.fullName}
          </h1>
          <p className="text-primary-100">
            إليك نظرة عامة على أداء مؤسسة النور التربوي اليوم
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                    <p className={`text-sm mt-1 ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change} من الشهر الماضي
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <div className={stat.color}>
                      {stat.icon}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Today's Overview */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 ml-2" />
                نظرة عامة على اليوم
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {todayStats.map((stat, index) => (
                  <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-white mb-2`}>
                      <div className={stat.color}>
                        {stat.icon}
                      </div>
                    </div>
                    <p className="text-2xl font-bold text-gray-900 mb-1">
                      {stat.value}
                    </p>
                    <p className="text-sm text-gray-600">
                      {stat.title}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle>الأنشطة الأخيرة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 space-x-reverse">
                    <div className="flex-shrink-0">
                      {activity.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        {activity.message}
                      </p>
                      {activity.amount && (
                        <p className="text-sm font-medium text-green-600">
                          {activity.amount}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات سريعة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button className="p-4 text-center bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm font-medium text-blue-900">تسجيل تلميذ</p>
              </button>
              <button className="p-4 text-center bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm font-medium text-green-900">تسجيل دفعة</p>
              </button>
              <button className="p-4 text-center bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <Calendar className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm font-medium text-purple-900">إدارة الجداول</p>
              </button>
              <button className="p-4 text-center bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                <TrendingUp className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                <p className="text-sm font-medium text-orange-900">التقارير</p>
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
