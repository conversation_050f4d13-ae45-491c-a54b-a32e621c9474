'use client'

import { useState, useEffect } from 'react'
import { 
  BarChart3, 
  Download, 
  Filter,
  TrendingUp,
  Users,
  DollarSign,
  Calendar,
  FileText,
  PieChart,
  Activity
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

interface ReportData {
  studentsReport: {
    totalStudents: number
    newStudents: number
    activeStudents: number
    maleStudents: number
    femaleStudents: number
    byLevel: { level: string; count: number }[]
  }
  financialReport: {
    totalRevenue: number
    monthlyRevenue: number
    expenses: number
    netProfit: number
    pendingPayments: number
    revenueByMonth: { month: string; amount: number }[]
  }
  attendanceReport: {
    averageAttendance: number
    totalClasses: number
    presentStudents: number
    absentStudents: number
    attendanceByDay: { day: string; percentage: number }[]
  }
  teachersReport: {
    totalTeachers: number
    activeTeachers: number
    totalSalaries: number
    averageSalary: number
    teachersBySubject: { subject: string; count: number }[]
  }
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [selectedReportType, setSelectedReportType] = useState('overview')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    const mockReportData: ReportData = {
      studentsReport: {
        totalStudents: 245,
        newStudents: 28,
        activeStudents: 238,
        maleStudents: 128,
        femaleStudents: 117,
        byLevel: [
          { level: 'المستوى الأول', count: 45 },
          { level: 'المستوى الثاني', count: 42 },
          { level: 'المستوى الثالث', count: 38 },
          { level: 'المستوى الرابع', count: 35 },
          { level: 'المستوى الخامس', count: 32 },
          { level: 'المستوى السادس', count: 28 },
          { level: 'الإعدادي', count: 25 }
        ]
      },
      financialReport: {
        totalRevenue: 1850000,
        monthlyRevenue: 425000,
        expenses: 320000,
        netProfit: 105000,
        pendingPayments: 15,
        revenueByMonth: [
          { month: 'يناير', amount: 380000 },
          { month: 'فبراير', amount: 395000 },
          { month: 'مارس', amount: 410000 },
          { month: 'أبريل', amount: 425000 },
          { month: 'مايو', amount: 440000 }
        ]
      },
      attendanceReport: {
        averageAttendance: 92.5,
        totalClasses: 156,
        presentStudents: 226,
        absentStudents: 19,
        attendanceByDay: [
          { day: 'الاثنين', percentage: 94 },
          { day: 'الثلاثاء', percentage: 91 },
          { day: 'الأربعاء', percentage: 95 },
          { day: 'الخميس', percentage: 89 },
          { day: 'الجمعة', percentage: 93 }
        ]
      },
      teachersReport: {
        totalTeachers: 18,
        activeTeachers: 16,
        totalSalaries: 285000,
        averageSalary: 17812,
        teachersBySubject: [
          { subject: 'الرياضيات', count: 4 },
          { subject: 'اللغة العربية', count: 3 },
          { subject: 'اللغة الفرنسية', count: 3 },
          { subject: 'اللغة الإنجليزية', count: 2 },
          { subject: 'التربية الإسلامية', count: 2 },
          { subject: 'النشاط العلمي', count: 2 },
          { subject: 'أخرى', count: 2 }
        ]
      }
    }
    
    setTimeout(() => {
      setReportData(mockReportData)
      setIsLoading(false)
    }, 1000)
  }, [])

  const reportTypes = [
    { key: 'overview', name: 'نظرة عامة', icon: BarChart3 },
    { key: 'students', name: 'تقرير التلاميذ', icon: Users },
    { key: 'financial', name: 'التقرير المالي', icon: DollarSign },
    { key: 'attendance', name: 'تقرير الحضور', icon: Calendar },
    { key: 'teachers', name: 'تقرير المعلمين', icon: Activity }
  ]

  const periods = [
    { key: 'week', name: 'أسبوعي' },
    { key: 'month', name: 'شهري' },
    { key: 'quarter', name: 'ربع سنوي' },
    { key: 'year', name: 'سنوي' }
  ]

  if (isLoading || !reportData) {
    return (
      <DashboardLayout>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري تحميل التقارير...</p>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            <p className="text-gray-600">تقارير شاملة وإحصائيات مفصلة عن أداء المؤسسة</p>
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              <Download className="w-4 h-4 ml-2" />
              تصدير PDF
            </Button>
            <Button variant="secondary" size="sm">
              <Download className="w-4 h-4 ml-2" />
              تصدير Excel
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع التقرير
                </label>
                <select
                  value={selectedReportType}
                  onChange={(e) => setSelectedReportType(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  {reportTypes.map(type => (
                    <option key={type.key} value={type.key}>{type.name}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفترة الزمنية
                </label>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  {periods.map(period => (
                    <option key={period.key} value={period.key}>{period.name}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-end">
                <Button className="w-full">
                  <Filter className="w-4 h-4 ml-2" />
                  تطبيق الفلتر
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Type Tabs */}
        <div className="flex flex-wrap gap-2">
          {reportTypes.map(type => {
            const IconComponent = type.icon
            return (
              <Button
                key={type.key}
                variant={selectedReportType === type.key ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setSelectedReportType(type.key)}
              >
                <IconComponent className="w-4 h-4 ml-2" />
                {type.name}
              </Button>
            )
          })}
        </div>

        {/* Overview Report */}
        {selectedReportType === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">إجمالي التلاميذ</p>
                    <p className="text-2xl font-bold text-blue-600">{reportData.studentsReport.totalStudents}</p>
                    <p className="text-sm text-green-600">+{reportData.studentsReport.newStudents} جديد</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">الإيرادات الشهرية</p>
                    <p className="text-2xl font-bold text-green-600">
                      {reportData.financialReport.monthlyRevenue.toLocaleString()} دج
                    </p>
                    <p className="text-sm text-green-600">+12.5%</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">معدل الحضور</p>
                    <p className="text-2xl font-bold text-purple-600">{reportData.attendanceReport.averageAttendance}%</p>
                    <p className="text-sm text-purple-600">ممتاز</p>
                  </div>
                  <Calendar className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">المعلمون النشطون</p>
                    <p className="text-2xl font-bold text-orange-600">{reportData.teachersReport.activeTeachers}</p>
                    <p className="text-sm text-gray-600">من {reportData.teachersReport.totalTeachers}</p>
                  </div>
                  <Activity className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Students Report */}
        {selectedReportType === 'students' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات التلاميذ</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إجمالي التلاميذ</span>
                    <span className="font-semibold">{reportData.studentsReport.totalStudents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">التلاميذ النشطون</span>
                    <span className="font-semibold text-green-600">{reportData.studentsReport.activeStudents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">التلاميذ الجدد</span>
                    <span className="font-semibold text-blue-600">{reportData.studentsReport.newStudents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">ذكور</span>
                    <span className="font-semibold">{reportData.studentsReport.maleStudents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إناث</span>
                    <span className="font-semibold">{reportData.studentsReport.femaleStudents}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>التوزيع حسب المستوى</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reportData.studentsReport.byLevel.map((level, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{level.level}</span>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-sm font-medium">{level.count}</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(level.count / reportData.studentsReport.totalStudents) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Financial Report */}
        {selectedReportType === 'financial' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>الملخص المالي</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إجمالي الإيرادات</span>
                    <span className="font-semibold text-green-600">
                      {reportData.financialReport.totalRevenue.toLocaleString()} دج
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">الإيرادات الشهرية</span>
                    <span className="font-semibold">
                      {reportData.financialReport.monthlyRevenue.toLocaleString()} دج
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">المصروفات</span>
                    <span className="font-semibold text-red-600">
                      {reportData.financialReport.expenses.toLocaleString()} دج
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">صافي الربح</span>
                    <span className="font-semibold text-blue-600">
                      {reportData.financialReport.netProfit.toLocaleString()} دج
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">المدفوعات المعلقة</span>
                    <span className="font-semibold text-yellow-600">
                      {reportData.financialReport.pendingPayments}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>الإيرادات الشهرية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reportData.financialReport.revenueByMonth.map((month, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{month.month}</span>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-sm font-medium">{month.amount.toLocaleString()} دج</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${(month.amount / Math.max(...reportData.financialReport.revenueByMonth.map(m => m.amount))) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Attendance Report */}
        {selectedReportType === 'attendance' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات الحضور</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">متوسط الحضور</span>
                    <span className="font-semibold text-green-600">{reportData.attendanceReport.averageAttendance}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إجمالي الحصص</span>
                    <span className="font-semibold">{reportData.attendanceReport.totalClasses}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">التلاميذ الحاضرون</span>
                    <span className="font-semibold text-green-600">{reportData.attendanceReport.presentStudents}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">التلاميذ الغائبون</span>
                    <span className="font-semibold text-red-600">{reportData.attendanceReport.absentStudents}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>الحضور حسب اليوم</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reportData.attendanceReport.attendanceByDay.map((day, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{day.day}</span>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-sm font-medium">{day.percentage}%</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${day.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Teachers Report */}
        {selectedReportType === 'teachers' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات المعلمين</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إجمالي المعلمين</span>
                    <span className="font-semibold">{reportData.teachersReport.totalTeachers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">المعلمون النشطون</span>
                    <span className="font-semibold text-green-600">{reportData.teachersReport.activeTeachers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">إجمالي الأجور</span>
                    <span className="font-semibold text-blue-600">
                      {reportData.teachersReport.totalSalaries.toLocaleString()} دج
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">متوسط الأجر</span>
                    <span className="font-semibold">
                      {reportData.teachersReport.averageSalary.toLocaleString()} دج
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>التوزيع حسب المادة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {reportData.teachersReport.teachersBySubject.map((subject, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{subject.subject}</span>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-sm font-medium">{subject.count}</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-purple-600 h-2 rounded-full" 
                            style={{ width: `${(subject.count / reportData.teachersReport.totalTeachers) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Export Actions */}
        <Card>
          <CardHeader>
            <CardTitle>تصدير التقارير</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="secondary" className="h-20 flex-col">
                <FileText className="w-6 h-6 mb-2" />
                <span className="text-sm">تقرير PDF</span>
              </Button>
              <Button variant="secondary" className="h-20 flex-col">
                <BarChart3 className="w-6 h-6 mb-2" />
                <span className="text-sm">ملف Excel</span>
              </Button>
              <Button variant="secondary" className="h-20 flex-col">
                <PieChart className="w-6 h-6 mb-2" />
                <span className="text-sm">رسوم بيانية</span>
              </Button>
              <Button variant="secondary" className="h-20 flex-col">
                <TrendingUp className="w-6 h-6 mb-2" />
                <span className="text-sm">تقرير مفصل</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
