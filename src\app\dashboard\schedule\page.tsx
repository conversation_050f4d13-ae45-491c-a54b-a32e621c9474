'use client'

import { useState, useEffect } from 'react'
import { 
  Calendar, 
  Plus, 
  Search, 
  Filter, 
  Download,
  Clock,
  Users,
  BookOpen,
  Edit,
  Trash2,
  Eye
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface ScheduleItem {
  id: string
  groupName: string
  teacherName: string
  subjectName: string
  levelName: string
  dayOfWeek: string
  sessionType: 'MORNING' | 'EVENING'
  startTime: string
  endTime: string
  studentsCount: number
  maxStudents: number
  isActive: boolean
}

interface WeekSchedule {
  [key: string]: ScheduleItem[]
}

export default function SchedulePage() {
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([])
  const [weekSchedule, setWeekSchedule] = useState<WeekSchedule>({})
  const [selectedDay, setSelectedDay] = useState('')
  const [selectedSession, setSelectedSession] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'table' | 'calendar'>('calendar')
  const [isLoading, setIsLoading] = useState(true)

  const daysOfWeek = [
    { key: 'MONDAY', name: 'الاثنين', hasEvening: true },
    { key: 'TUESDAY', name: 'الثلاثاء', hasEvening: true },
    { key: 'WEDNESDAY', name: 'الأربعاء', hasEvening: false },
    { key: 'THURSDAY', name: 'الخميس', hasEvening: true },
    { key: 'FRIDAY', name: 'الجمعة', hasEvening: true }
  ]

  const timeSlots = {
    MORNING: [
      { start: '08:00', end: '09:30' },
      { start: '09:45', end: '11:15' },
      { start: '11:30', end: '12:00' }
    ],
    EVENING: [
      { start: '14:00', end: '15:30' },
      { start: '15:45', end: '17:15' },
      { start: '17:30', end: '18:00' }
    ]
  }

  // Mock data for demonstration
  useEffect(() => {
    const mockScheduleItems: ScheduleItem[] = [
      {
        id: '1',
        groupName: 'مجموعة الرياضيات أ',
        teacherName: 'أحمد محمد',
        subjectName: 'الرياضيات',
        levelName: 'المستوى الثالث ابتدائي',
        dayOfWeek: 'MONDAY',
        sessionType: 'MORNING',
        startTime: '08:00',
        endTime: '09:30',
        studentsCount: 25,
        maxStudents: 30,
        isActive: true
      },
      {
        id: '2',
        groupName: 'مجموعة العربية أ',
        teacherName: 'فاطمة علي',
        subjectName: 'اللغة العربية',
        levelName: 'المستوى الثاني ابتدائي',
        dayOfWeek: 'MONDAY',
        sessionType: 'MORNING',
        startTime: '09:45',
        endTime: '11:15',
        studentsCount: 22,
        maxStudents: 25,
        isActive: true
      },
      {
        id: '3',
        groupName: 'مجموعة الفرنسية أ',
        teacherName: 'محمد حسن',
        subjectName: 'اللغة الفرنسية',
        levelName: 'المستوى الرابع ابتدائي',
        dayOfWeek: 'MONDAY',
        sessionType: 'EVENING',
        startTime: '14:00',
        endTime: '15:30',
        studentsCount: 28,
        maxStudents: 30,
        isActive: true
      },
      {
        id: '4',
        groupName: 'مجموعة الرياضيات ب',
        teacherName: 'أحمد محمد',
        subjectName: 'الرياضيات',
        levelName: 'المستوى الخامس ابتدائي',
        dayOfWeek: 'TUESDAY',
        sessionType: 'MORNING',
        startTime: '08:00',
        endTime: '09:30',
        studentsCount: 20,
        maxStudents: 25,
        isActive: true
      },
      {
        id: '5',
        groupName: 'مجموعة الإسلامية أ',
        teacherName: 'خديجة أحمد',
        subjectName: 'التربية الإسلامية',
        levelName: 'المستوى الأول',
        dayOfWeek: 'WEDNESDAY',
        sessionType: 'MORNING',
        startTime: '08:00',
        endTime: '09:30',
        studentsCount: 18,
        maxStudents: 20,
        isActive: true
      }
    ]
    
    setTimeout(() => {
      setScheduleItems(mockScheduleItems)
      
      // Group by day
      const grouped = mockScheduleItems.reduce((acc, item) => {
        if (!acc[item.dayOfWeek]) {
          acc[item.dayOfWeek] = []
        }
        acc[item.dayOfWeek].push(item)
        return acc
      }, {} as WeekSchedule)
      
      setWeekSchedule(grouped)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredScheduleItems = scheduleItems.filter(item => {
    const matchesSearch = 
      item.groupName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.teacherName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.subjectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.levelName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesDay = selectedDay === '' || item.dayOfWeek === selectedDay
    const matchesSession = selectedSession === '' || item.sessionType === selectedSession
    
    return matchesSearch && matchesDay && matchesSession
  })

  const stats = {
    totalClasses: scheduleItems.length,
    activeClasses: scheduleItems.filter(s => s.isActive).length,
    totalStudents: scheduleItems.reduce((sum, s) => sum + s.studentsCount, 0),
    morningClasses: scheduleItems.filter(s => s.sessionType === 'MORNING').length,
    eveningClasses: scheduleItems.filter(s => s.sessionType === 'EVENING').length,
    averageStudents: scheduleItems.length > 0 ? Math.round(scheduleItems.reduce((sum, s) => sum + s.studentsCount, 0) / scheduleItems.length) : 0
  }

  const getSubjectColor = (subject: string) => {
    const colors = {
      'الرياضيات': 'bg-blue-100 text-blue-800 border-blue-200',
      'اللغة العربية': 'bg-green-100 text-green-800 border-green-200',
      'اللغة الفرنسية': 'bg-purple-100 text-purple-800 border-purple-200',
      'اللغة الإنجليزية': 'bg-red-100 text-red-800 border-red-200',
      'التربية الإسلامية': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'النشاط العلمي': 'bg-indigo-100 text-indigo-800 border-indigo-200'
    }
    return colors[subject as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">الجداول والحصص</h1>
            <p className="text-gray-600">إدارة الجداول الدراسية وتنظيم الحصص</p>
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              <Download className="w-4 h-4 ml-2" />
              طباعة الجدول
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 ml-2" />
              إضافة حصة جديدة
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي الحصص</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalClasses}</p>
                </div>
                <Calendar className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">الحصص النشطة</p>
                  <p className="text-2xl font-bold text-green-600">{stats.activeClasses}</p>
                </div>
                <BookOpen className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي التلاميذ</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.totalStudents}</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">صباحي / مسائي</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.morningClasses} / {stats.eveningClasses}</p>
                </div>
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* View Mode Toggle */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex gap-2">
                <Button
                  variant={viewMode === 'calendar' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('calendar')}
                >
                  <Calendar className="w-4 h-4 ml-2" />
                  عرض الجدول
                </Button>
                <Button
                  variant={viewMode === 'table' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <BookOpen className="w-4 h-4 ml-2" />
                  عرض القائمة
                </Button>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full sm:w-auto">
                <Input
                  placeholder="البحث..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="w-4 h-4" />}
                />
                
                <select
                  value={selectedDay}
                  onChange={(e) => setSelectedDay(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">جميع الأيام</option>
                  {daysOfWeek.map(day => (
                    <option key={day.key} value={day.key}>{day.name}</option>
                  ))}
                </select>
                
                <select
                  value={selectedSession}
                  onChange={(e) => setSelectedSession(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">جميع الفترات</option>
                  <option value="MORNING">صباحي</option>
                  <option value="EVENING">مسائي</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Calendar View */}
        {viewMode === 'calendar' && (
          <Card>
            <CardHeader>
              <CardTitle>الجدول الأسبوعي</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">جاري التحميل...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 min-w-full">
                    {daysOfWeek.map(day => (
                      <div key={day.key} className="border rounded-lg p-4">
                        <h3 className="font-semibold text-center mb-4 text-gray-900">
                          {day.name}
                        </h3>
                        
                        {/* Morning Session */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-600 mb-2">الفترة الصباحية</h4>
                          <div className="space-y-2">
                            {weekSchedule[day.key]?.filter(item => item.sessionType === 'MORNING').map(item => (
                              <div
                                key={item.id}
                                className={`p-2 rounded border text-xs ${getSubjectColor(item.subjectName)}`}
                              >
                                <div className="font-medium">{item.subjectName}</div>
                                <div className="text-xs opacity-75">{item.teacherName}</div>
                                <div className="text-xs opacity-75">{item.startTime} - {item.endTime}</div>
                                <div className="text-xs opacity-75">{item.studentsCount}/{item.maxStudents} تلميذ</div>
                              </div>
                            )) || <div className="text-xs text-gray-400 p-2">لا توجد حصص</div>}
                          </div>
                        </div>
                        
                        {/* Evening Session */}
                        {day.hasEvening && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-600 mb-2">الفترة المسائية</h4>
                            <div className="space-y-2">
                              {weekSchedule[day.key]?.filter(item => item.sessionType === 'EVENING').map(item => (
                                <div
                                  key={item.id}
                                  className={`p-2 rounded border text-xs ${getSubjectColor(item.subjectName)}`}
                                >
                                  <div className="font-medium">{item.subjectName}</div>
                                  <div className="text-xs opacity-75">{item.teacherName}</div>
                                  <div className="text-xs opacity-75">{item.startTime} - {item.endTime}</div>
                                  <div className="text-xs opacity-75">{item.studentsCount}/{item.maxStudents} تلميذ</div>
                                </div>
                              )) || <div className="text-xs text-gray-400 p-2">لا توجد حصص</div>}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <Card>
            <CardHeader>
              <CardTitle>قائمة الحصص ({filteredScheduleItems.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">جاري التحميل...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المجموعة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المعلم
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المادة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          اليوم والوقت
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          التلاميذ
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredScheduleItems.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {item.groupName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.levelName}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.teacherName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSubjectColor(item.subjectName)}`}>
                              {item.subjectName}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {daysOfWeek.find(d => d.key === item.dayOfWeek)?.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.startTime} - {item.endTime} ({item.sessionType === 'MORNING' ? 'صباحي' : 'مسائي'})
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {item.studentsCount} / {item.maxStudents}
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${(item.studentsCount / item.maxStudents) * 100}%` }}
                              ></div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2 space-x-reverse">
                              <button className="text-blue-600 hover:text-blue-900">
                                <Eye className="w-4 h-4" />
                              </button>
                              <button className="text-green-600 hover:text-green-900">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="text-red-600 hover:text-red-900">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
