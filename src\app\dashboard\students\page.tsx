'use client'

import { useState, useEffect } from 'react'
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  Download,
  Edit,
  Trash2,
  Eye,
  UserPlus,
  BarCode
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface Student {
  id: string
  studentCode: string
  barcode: string
  firstName: string
  lastName: string
  birthDate: string
  gender: 'MALE' | 'FEMALE'
  parentPhone: string
  parentName: string
  levelName: string
  feesAmount: number
  paidAmount: number
  remainingAmount: number
  isActive: boolean
}

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLevel, setSelectedLevel] = useState('')
  const [selectedGender, setSelectedGender] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    const mockStudents: Student[] = [
      {
        id: '1',
        studentCode: 'STD001',
        barcode: '1234567890123',
        firstName: 'أحمد',
        lastName: 'محمد',
        birthDate: '2010-05-15',
        gender: 'MALE',
        parentPhone: '+213555123456',
        parentName: 'محمد أحمد',
        levelName: 'المستوى الثالث ابتدائي',
        feesAmount: 8000,
        paidAmount: 6000,
        remainingAmount: 2000,
        isActive: true
      },
      {
        id: '2',
        studentCode: 'STD002',
        barcode: '1234567890124',
        firstName: 'فاطمة',
        lastName: 'علي',
        birthDate: '2011-03-20',
        gender: 'FEMALE',
        parentPhone: '+213555123457',
        parentName: 'علي فاطمة',
        levelName: 'المستوى الثاني ابتدائي',
        feesAmount: 7500,
        paidAmount: 7500,
        remainingAmount: 0,
        isActive: true
      },
      {
        id: '3',
        studentCode: 'STD003',
        barcode: '1234567890125',
        firstName: 'خالد',
        lastName: 'حسن',
        birthDate: '2009-08-10',
        gender: 'MALE',
        parentPhone: '+213555123458',
        parentName: 'حسن خالد',
        levelName: 'المستوى الخامس ابتدائي',
        feesAmount: 9000,
        paidAmount: 4500,
        remainingAmount: 4500,
        isActive: true
      }
    ]
    
    setTimeout(() => {
      setStudents(mockStudents)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredStudents = students.filter(student => {
    const matchesSearch = 
      student.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.studentCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.parentName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesLevel = selectedLevel === '' || student.levelName.includes(selectedLevel)
    const matchesGender = selectedGender === '' || student.gender === selectedGender
    
    return matchesSearch && matchesLevel && matchesGender
  })

  const stats = {
    total: students.length,
    active: students.filter(s => s.isActive).length,
    male: students.filter(s => s.gender === 'MALE').length,
    female: students.filter(s => s.gender === 'FEMALE').length,
    totalFees: students.reduce((sum, s) => sum + s.feesAmount, 0),
    totalPaid: students.reduce((sum, s) => sum + s.paidAmount, 0),
    totalRemaining: students.reduce((sum, s) => sum + s.remainingAmount, 0)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة التلاميذ</h1>
            <p className="text-gray-600">إدارة بيانات التلاميذ والمتابعة الأكاديمية</p>
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              <Download className="w-4 h-4 ml-2" />
              تصدير
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 ml-2" />
              تسجيل تلميذ جديد
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي التلاميذ</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <Users className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">التلاميذ النشطون</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <UserPlus className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">ذكور / إناث</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.male} / {stats.female}</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المستحقات المتبقية</p>
                  <p className="text-2xl font-bold text-red-600">{stats.totalRemaining.toLocaleString()} دج</p>
                </div>
                <BarCode className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                placeholder="البحث بالاسم أو رقم التلميذ..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
              />
              
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">جميع المستويات</option>
                <option value="الأول">المستوى الأول</option>
                <option value="الثاني">المستوى الثاني</option>
                <option value="الثالث">المستوى الثالث</option>
                <option value="الرابع">المستوى الرابع</option>
                <option value="الخامس">المستوى الخامس</option>
                <option value="السادس">المستوى السادس</option>
              </select>
              
              <select
                value={selectedGender}
                onChange={(e) => setSelectedGender(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">الجنس</option>
                <option value="MALE">ذكر</option>
                <option value="FEMALE">أنثى</option>
              </select>
              
              <Button variant="secondary" className="w-full">
                <Filter className="w-4 h-4 ml-2" />
                تصفية متقدمة
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Students Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة التلاميذ ({filteredStudents.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التلميذ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستوى
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ولي الأمر
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الرسوم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredStudents.map((student) => (
                      <tr key={student.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <span className="text-sm font-medium text-primary-700">
                                  {student.firstName.charAt(0)}
                                </span>
                              </div>
                            </div>
                            <div className="mr-4">
                              <div className="text-sm font-medium text-gray-900">
                                {student.firstName} {student.lastName}
                              </div>
                              <div className="text-sm text-gray-500">
                                {student.studentCode}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {student.levelName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.parentName}</div>
                          <div className="text-sm text-gray-500">{student.parentPhone}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {student.feesAmount.toLocaleString()} دج
                          </div>
                          <div className="text-sm text-gray-500">
                            متبقي: {student.remainingAmount.toLocaleString()} دج
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            student.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {student.isActive ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2 space-x-reverse">
                            <button className="text-blue-600 hover:text-blue-900">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
