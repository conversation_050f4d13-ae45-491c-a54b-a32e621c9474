'use client'

import { useState, useEffect } from 'react'
import { 
  GraduationCap, 
  Plus, 
  Search, 
  Filter, 
  Download,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Users,
  Calendar
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface Teacher {
  id: string
  teacherCode: string
  firstName: string
  lastName: string
  email: string
  phone: string
  specialization: string
  hourlyRate: number
  paymentMethod: 'HOURLY' | 'MONTHLY' | 'PER_STUDENT' | 'FIXED' | 'PERCENTAGE'
  totalStudents: number
  totalGroups: number
  monthlyEarnings: number
  isActive: boolean
}

export default function TeachersPage() {
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSpecialization, setSelectedSpecialization] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    const mockTeachers: Teacher[] = [
      {
        id: '1',
        teacherCode: 'TCH001',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: '<EMAIL>',
        phone: '+213555123456',
        specialization: 'الرياضيات',
        hourlyRate: 1500,
        paymentMethod: 'HOURLY',
        totalStudents: 45,
        totalGroups: 3,
        monthlyEarnings: 85000,
        isActive: true
      },
      {
        id: '2',
        teacherCode: 'TCH002',
        firstName: 'فاطمة',
        lastName: 'علي',
        email: '<EMAIL>',
        phone: '+213555123457',
        specialization: 'اللغة العربية',
        hourlyRate: 2000,
        paymentMethod: 'PER_STUDENT',
        totalStudents: 38,
        totalGroups: 2,
        monthlyEarnings: 76000,
        isActive: true
      },
      {
        id: '3',
        teacherCode: 'TCH003',
        firstName: 'محمد',
        lastName: 'حسن',
        email: '<EMAIL>',
        phone: '+213555123458',
        specialization: 'اللغة الفرنسية',
        hourlyRate: 50000,
        paymentMethod: 'MONTHLY',
        totalStudents: 52,
        totalGroups: 4,
        monthlyEarnings: 50000,
        isActive: true
      },
      {
        id: '4',
        teacherCode: 'TCH004',
        firstName: 'خديجة',
        lastName: 'أحمد',
        email: '<EMAIL>',
        phone: '+213555123459',
        specialization: 'التربية الإسلامية',
        hourlyRate: 1200,
        paymentMethod: 'HOURLY',
        totalStudents: 28,
        totalGroups: 2,
        monthlyEarnings: 42000,
        isActive: false
      }
    ]
    
    setTimeout(() => {
      setTeachers(mockTeachers)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredTeachers = teachers.filter(teacher => {
    const matchesSearch = 
      teacher.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      teacher.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      teacher.teacherCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      teacher.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesSpecialization = selectedSpecialization === '' || teacher.specialization === selectedSpecialization
    const matchesPaymentMethod = selectedPaymentMethod === '' || teacher.paymentMethod === selectedPaymentMethod
    
    return matchesSearch && matchesSpecialization && matchesPaymentMethod
  })

  const stats = {
    total: teachers.length,
    active: teachers.filter(t => t.isActive).length,
    totalStudents: teachers.reduce((sum, t) => sum + t.totalStudents, 0),
    totalGroups: teachers.reduce((sum, t) => sum + t.totalGroups, 0),
    totalEarnings: teachers.reduce((sum, t) => sum + t.monthlyEarnings, 0),
    avgEarnings: teachers.length > 0 ? teachers.reduce((sum, t) => sum + t.monthlyEarnings, 0) / teachers.length : 0
  }

  const getPaymentMethodText = (method: string) => {
    const methods = {
      HOURLY: 'حسب الساعة',
      MONTHLY: 'شهري',
      PER_STUDENT: 'حسب التلميذ',
      FIXED: 'ثابت',
      PERCENTAGE: 'نسبة مئوية'
    }
    return methods[method as keyof typeof methods] || method
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المعلمين</h1>
            <p className="text-gray-600">إدارة بيانات المعلمين والأجور</p>
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              <Download className="w-4 h-4 ml-2" />
              تصدير
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 ml-2" />
              إضافة معلم جديد
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المعلمين</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <GraduationCap className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المعلمون النشطون</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <Users className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي التلاميذ</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.totalStudents}</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي الأجور</p>
                  <p className="text-2xl font-bold text-green-600">{stats.totalEarnings.toLocaleString()} دج</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                placeholder="البحث بالاسم أو رقم المعلم..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
              />
              
              <select
                value={selectedSpecialization}
                onChange={(e) => setSelectedSpecialization(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">جميع التخصصات</option>
                <option value="الرياضيات">الرياضيات</option>
                <option value="اللغة العربية">اللغة العربية</option>
                <option value="اللغة الفرنسية">اللغة الفرنسية</option>
                <option value="اللغة الإنجليزية">اللغة الإنجليزية</option>
                <option value="التربية الإسلامية">التربية الإسلامية</option>
                <option value="النشاط العلمي">النشاط العلمي</option>
              </select>
              
              <select
                value={selectedPaymentMethod}
                onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">طريقة الدفع</option>
                <option value="HOURLY">حسب الساعة</option>
                <option value="MONTHLY">شهري</option>
                <option value="PER_STUDENT">حسب التلميذ</option>
                <option value="FIXED">ثابت</option>
                <option value="PERCENTAGE">نسبة مئوية</option>
              </select>
              
              <Button variant="secondary" className="w-full">
                <Filter className="w-4 h-4 ml-2" />
                تصفية متقدمة
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Teachers Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المعلمين ({filteredTeachers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المعلم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التخصص
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التلاميذ/المجموعات
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        طريقة الدفع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الأجر الشهري
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredTeachers.map((teacher) => (
                      <tr key={teacher.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <span className="text-sm font-medium text-primary-700">
                                  {teacher.firstName.charAt(0)}
                                </span>
                              </div>
                            </div>
                            <div className="mr-4">
                              <div className="text-sm font-medium text-gray-900">
                                {teacher.firstName} {teacher.lastName}
                              </div>
                              <div className="text-sm text-gray-500">
                                {teacher.teacherCode}
                              </div>
                              <div className="text-sm text-gray-500">
                                {teacher.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {teacher.specialization}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {teacher.totalStudents} تلميذ
                          </div>
                          <div className="text-sm text-gray-500">
                            {teacher.totalGroups} مجموعة
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {getPaymentMethodText(teacher.paymentMethod)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {teacher.hourlyRate.toLocaleString()} دج
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {teacher.monthlyEarnings.toLocaleString()} دج
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            teacher.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {teacher.isActive ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2 space-x-reverse">
                            <button className="text-blue-600 hover:text-blue-900">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="text-purple-600 hover:text-purple-900">
                              <DollarSign className="w-4 h-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
