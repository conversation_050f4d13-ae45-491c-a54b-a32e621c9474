import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'SBEA - نظام إدارة مؤسسة النور التربوي',
  description: 'نظام إدارة مدرسي متكامل لمؤسسة النور التربوي',
  keywords: 'إدارة مدرسية, نظام تعليمي, مؤسسة النور التربوي, SBEA',
  authors: [{ name: 'SBEA Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      </head>
      <body className="min-h-screen bg-gray-50 font-arabic">
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  )
}
