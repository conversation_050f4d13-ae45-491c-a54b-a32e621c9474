'use client'

import { useState } from 'react'
import Link from 'next/link'
import { BookOpen, Users, Calculator, Calendar, BarChart3, Settings, LogIn } from 'lucide-react'

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(false)

  const features = [
    {
      icon: <Users className="w-8 h-8" />,
      title: "إدارة التلاميذ والمعلمين",
      description: "نظام شامل لإدارة بيانات التلاميذ والمعلمين مع إمكانية تتبع الأداء"
    },
    {
      icon: <Calculator className="w-8 h-8" />,
      title: "النظام المالي المتكامل",
      description: "إدارة الرسوم والفواتير وأجور المعلمين مع تقارير مالية دقيقة"
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "الجداول والحصص",
      description: "تنظيم الجداول الدراسية والحصص حسب المستويات والمواد"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "التقارير والإحصائيات",
      description: "تقارير شاملة وإحصائيات دقيقة لمتابعة أداء المؤسسة"
    },
    {
      icon: <BookOpen className="w-8 h-8" />,
      title: "إدارة المناهج",
      description: "تنظيم المواد الدراسية ومكوناتها حسب المستويات التعليمية"
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "إعدادات متقدمة",
      description: "تخصيص النظام حسب احتياجات المؤسسة مع صلاحيات متعددة"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-primary-600 p-2 rounded-lg">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">SBEA</h1>
                <p className="text-sm text-gray-600">مؤسسة النور التربوي</p>
              </div>
            </div>
            <Link
              href="/auth/login"
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <LogIn className="w-4 h-4 ml-2" />
              تسجيل الدخول
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            نظام إدارة مدرسي
            <span className="text-primary-600 block">متكامل ومتطور</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            SBEA هو الحل الأمثل لإدارة المؤسسات التعليمية بطريقة احترافية ومبتكرة، 
            مع تكامل WhatsApp ونظام الكود بار المتقدم
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/login"
              className="inline-flex items-center justify-center px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-lg font-medium"
            >
              ابدأ الآن
            </Link>
            <Link
              href="/demo"
              className="inline-flex items-center justify-center px-8 py-3 bg-white text-primary-600 border-2 border-primary-600 rounded-lg hover:bg-primary-50 transition-colors text-lg font-medium"
            >
              جرب النسخة التجريبية
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              مميزات النظام
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نظام شامل يغطي جميع احتياجات إدارة المؤسسات التعليمية
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-gray-50 p-6 rounded-xl hover:shadow-lg transition-shadow"
              >
                <div className="text-primary-600 mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-4 space-x-reverse mb-4">
              <div className="bg-primary-600 p-2 rounded-lg">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold">SBEA</h3>
            </div>
            <p className="text-gray-400 mb-4">
              نظام إدارة مدرسي متكامل لمؤسسة النور التربوي
            </p>
            <p className="text-sm text-gray-500">
              © 2024 SBEA. جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
