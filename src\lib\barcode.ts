// Barcode Generation and Management Service
export interface BarcodeData {
  studentId: string
  studentCode: string
  barcode: string
  generatedAt: Date
  isActive: boolean
}

export class BarcodeService {
  // Generate a unique barcode for a student
  static generateBarcode(studentCode: string): string {
    // Create a unique barcode based on student code and timestamp
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2, 8)
    
    // Combine student code with timestamp and random string
    const combined = `${studentCode}${timestamp}${random}`
    
    // Generate a 13-digit barcode (EAN-13 format)
    let barcode = ''
    for (let i = 0; i < combined.length && barcode.length < 12; i++) {
      const char = combined[i]
      if (char >= '0' && char <= '9') {
        barcode += char
      } else {
        // Convert letters to numbers
        barcode += (char.charCodeAt(0) % 10).toString()
      }
    }
    
    // Pad with zeros if needed
    while (barcode.length < 12) {
      barcode += '0'
    }
    
    // Calculate check digit
    const checkDigit = this.calculateCheckDigit(barcode)
    
    return barcode + checkDigit
  }

  // Calculate EAN-13 check digit
  private static calculateCheckDigit(barcode: string): string {
    let sum = 0
    for (let i = 0; i < 12; i++) {
      const digit = parseInt(barcode[i])
      if (i % 2 === 0) {
        sum += digit
      } else {
        sum += digit * 3
      }
    }
    
    const checkDigit = (10 - (sum % 10)) % 10
    return checkDigit.toString()
  }

  // Validate barcode format
  static validateBarcode(barcode: string): boolean {
    if (barcode.length !== 13) {
      return false
    }
    
    if (!/^\d{13}$/.test(barcode)) {
      return false
    }
    
    const checkDigit = this.calculateCheckDigit(barcode.substring(0, 12))
    return checkDigit === barcode[12]
  }

  // Generate barcode data URL for display
  static generateBarcodeDataURL(barcode: string): string {
    // This is a simplified implementation
    // In a real application, you would use a proper barcode library like JsBarcode
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      return ''
    }
    
    canvas.width = 200
    canvas.height = 60
    
    // Clear canvas
    ctx.fillStyle = 'white'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // Draw barcode bars
    ctx.fillStyle = 'black'
    const barWidth = 2
    let x = 10
    
    for (let i = 0; i < barcode.length; i++) {
      const digit = parseInt(barcode[i])
      const pattern = this.getBarcodePattern(digit)
      
      for (let j = 0; j < pattern.length; j++) {
        if (pattern[j] === '1') {
          ctx.fillRect(x, 10, barWidth, 30)
        }
        x += barWidth
      }
    }
    
    // Draw barcode text
    ctx.fillStyle = 'black'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(barcode, canvas.width / 2, 55)
    
    return canvas.toDataURL()
  }

  // Get barcode pattern for a digit (simplified)
  private static getBarcodePattern(digit: number): string {
    const patterns = [
      '0001101', // 0
      '0011001', // 1
      '0010011', // 2
      '0111101', // 3
      '0100011', // 4
      '0110001', // 5
      '0101111', // 6
      '0111011', // 7
      '0110111', // 8
      '0001011'  // 9
    ]
    
    return patterns[digit] || patterns[0]
  }

  // Generate student card with barcode
  static generateStudentCard(student: any): string {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      return ''
    }
    
    canvas.width = 400
    canvas.height = 250
    
    // Card background
    ctx.fillStyle = '#f8f9fa'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // Card border
    ctx.strokeStyle = '#dee2e6'
    ctx.lineWidth = 2
    ctx.strokeRect(5, 5, canvas.width - 10, canvas.height - 10)
    
    // Header
    ctx.fillStyle = '#007bff'
    ctx.fillRect(10, 10, canvas.width - 20, 50)
    
    // School name
    ctx.fillStyle = 'white'
    ctx.font = 'bold 18px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('مؤسسة النور التربوي', canvas.width / 2, 35)
    
    // Student info
    ctx.fillStyle = 'black'
    ctx.font = '16px Arial'
    ctx.textAlign = 'right'
    
    ctx.fillText(`الاسم: ${student.firstName} ${student.lastName}`, canvas.width - 20, 90)
    ctx.fillText(`رقم التلميذ: ${student.studentCode}`, canvas.width - 20, 115)
    ctx.fillText(`المستوى: ${student.levelName}`, canvas.width - 20, 140)
    
    // Barcode
    const barcodeDataURL = this.generateBarcodeDataURL(student.barcode)
    if (barcodeDataURL) {
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 20, 160, 180, 60)
      }
      img.src = barcodeDataURL
    }
    
    return canvas.toDataURL()
  }

  // Scan barcode and get student info
  static async scanBarcode(barcode: string): Promise<any | null> {
    // Validate barcode format
    if (!this.validateBarcode(barcode)) {
      throw new Error('Invalid barcode format')
    }
    
    // In a real application, this would query the database
    // For now, we'll return mock data
    return {
      studentId: '1',
      studentCode: 'STD001',
      firstName: 'أحمد',
      lastName: 'محمد',
      levelName: 'المستوى الثالث ابتدائي',
      barcode: barcode,
      isActive: true
    }
  }

  // Print barcode labels
  static printBarcodeLabels(students: any[]): void {
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      return
    }
    
    let html = `
      <html>
        <head>
          <title>طباعة الكود بار</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            .label { 
              width: 200px; 
              height: 100px; 
              border: 1px solid #ccc; 
              margin: 10px; 
              padding: 10px; 
              display: inline-block;
              text-align: center;
            }
            .barcode { margin: 10px 0; }
            @media print {
              .label { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
    `
    
    students.forEach(student => {
      html += `
        <div class="label">
          <div><strong>${student.firstName} ${student.lastName}</strong></div>
          <div>${student.studentCode}</div>
          <div class="barcode">
            <img src="${this.generateBarcodeDataURL(student.barcode)}" alt="Barcode" />
          </div>
        </div>
      `
    })
    
    html += `
        </body>
      </html>
    `
    
    printWindow.document.write(html)
    printWindow.document.close()
    printWindow.print()
  }

  // Generate QR code for student (alternative to barcode)
  static generateQRCode(studentData: any): string {
    // This would typically use a QR code library
    // For now, we'll return a placeholder
    const data = JSON.stringify({
      studentId: studentData.id,
      studentCode: studentData.studentCode,
      name: `${studentData.firstName} ${studentData.lastName}`
    })
    
    // In a real implementation, use a QR code library like qrcode.js
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100" height="100" fill="white"/>
        <text x="50" y="50" text-anchor="middle" font-size="8">QR Code</text>
      </svg>
    `)}`
  }
}

// Export singleton instance
export const barcodeService = BarcodeService
