// WhatsApp Integration Service
export interface WhatsAppMessage {
  to: string
  message: string
  type: 'text' | 'template' | 'media'
  templateName?: string
  templateParams?: string[]
  mediaUrl?: string
}

export interface WhatsAppContact {
  phone: string
  name: string
  type: 'student' | 'parent' | 'teacher'
}

export interface WhatsAppGroup {
  id: string
  name: string
  description: string
  members: WhatsAppContact[]
  createdAt: Date
}

export class WhatsAppService {
  private apiUrl: string
  private token: string
  private phoneNumberId: string

  constructor() {
    this.apiUrl = process.env.WHATSAPP_API_URL || 'https://graph.facebook.com/v18.0'
    this.token = process.env.WHATSAPP_TOKEN || ''
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID || ''
  }

  // Send a text message
  async sendTextMessage(to: string, message: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messaging_product: 'whatsapp',
          to: to,
          type: 'text',
          text: {
            body: message
          }
        })
      })

      return response.ok
    } catch (error) {
      console.error('Error sending WhatsApp message:', error)
      return false
    }
  }

  // Send a template message
  async sendTemplateMessage(to: string, templateName: string, params: string[] = []): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messaging_product: 'whatsapp',
          to: to,
          type: 'template',
          template: {
            name: templateName,
            language: {
              code: 'ar'
            },
            components: params.length > 0 ? [{
              type: 'body',
              parameters: params.map(param => ({
                type: 'text',
                text: param
              }))
            }] : []
          }
        })
      })

      return response.ok
    } catch (error) {
      console.error('Error sending WhatsApp template:', error)
      return false
    }
  }

  // Send bulk messages
  async sendBulkMessages(messages: WhatsAppMessage[]): Promise<{ success: number; failed: number }> {
    let success = 0
    let failed = 0

    for (const message of messages) {
      try {
        let result = false
        
        if (message.type === 'text') {
          result = await this.sendTextMessage(message.to, message.message)
        } else if (message.type === 'template' && message.templateName) {
          result = await this.sendTemplateMessage(message.to, message.templateName, message.templateParams)
        }

        if (result) {
          success++
        } else {
          failed++
        }

        // Add delay between messages to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        failed++
      }
    }

    return { success, failed }
  }

  // Send payment reminder
  async sendPaymentReminder(studentName: string, parentPhone: string, amount: number, dueDate: string): Promise<boolean> {
    const message = `
مرحباً،

نذكركم بأن هناك مستحقات مالية للتلميذ/ة ${studentName}:

💰 المبلغ: ${amount.toLocaleString()} دج
📅 تاريخ الاستحقاق: ${dueDate}

يرجى تسديد المبلغ في أقرب وقت ممكن.

شكراً لكم
مؤسسة النور التربوي
    `.trim()

    return this.sendTextMessage(parentPhone, message)
  }

  // Send class schedule update
  async sendScheduleUpdate(parentPhone: string, studentName: string, className: string, newTime: string, date: string): Promise<boolean> {
    const message = `
إشعار تغيير موعد الحصة

عزيزي ولي أمر التلميذ/ة ${studentName}،

تم تغيير موعد حصة ${className}:
📅 التاريخ: ${date}
🕐 الموعد الجديد: ${newTime}

نعتذر عن أي إزعاج
مؤسسة النور التربوي
    `.trim()

    return this.sendTextMessage(parentPhone, message)
  }

  // Send attendance notification
  async sendAttendanceNotification(parentPhone: string, studentName: string, date: string, status: 'present' | 'absent'): Promise<boolean> {
    const statusText = status === 'present' ? 'حاضر' : 'غائب'
    const emoji = status === 'present' ? '✅' : '❌'
    
    const message = `
${emoji} إشعار الحضور

التلميذ/ة: ${studentName}
التاريخ: ${date}
الحالة: ${statusText}

مؤسسة النور التربوي
    `.trim()

    return this.sendTextMessage(parentPhone, message)
  }

  // Send exam results
  async sendExamResults(parentPhone: string, studentName: string, subject: string, grade: number, maxGrade: number): Promise<boolean> {
    const percentage = Math.round((grade / maxGrade) * 100)
    const emoji = percentage >= 80 ? '🎉' : percentage >= 60 ? '👍' : '📚'
    
    const message = `
${emoji} نتائج الامتحان

التلميذ/ة: ${studentName}
المادة: ${subject}
الدرجة: ${grade}/${maxGrade} (${percentage}%)

${percentage >= 80 ? 'ممتاز! أحسنت' : percentage >= 60 ? 'جيد، واصل التقدم' : 'يحتاج لمزيد من المراجعة'}

مؤسسة النور التربوي
    `.trim()

    return this.sendTextMessage(parentPhone, message)
  }

  // Create WhatsApp group (Note: This requires WhatsApp Business API approval)
  async createGroup(name: string, description: string, members: string[]): Promise<string | null> {
    // This is a placeholder - actual implementation depends on WhatsApp Business API
    // For now, we'll simulate group creation
    console.log(`Creating WhatsApp group: ${name} with ${members.length} members`)
    return `group_${Date.now()}`
  }

  // Format phone number for WhatsApp
  formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters
    let cleaned = phone.replace(/\D/g, '')
    
    // Add country code if not present (assuming Algeria +213)
    if (!cleaned.startsWith('213')) {
      if (cleaned.startsWith('0')) {
        cleaned = '213' + cleaned.substring(1)
      } else {
        cleaned = '213' + cleaned
      }
    }
    
    return cleaned
  }

  // Validate phone number
  isValidPhoneNumber(phone: string): boolean {
    const formatted = this.formatPhoneNumber(phone)
    return /^213[5-7]\d{8}$/.test(formatted)
  }
}

// Pre-defined message templates
export const WhatsAppTemplates = {
  PAYMENT_REMINDER: 'payment_reminder',
  SCHEDULE_UPDATE: 'schedule_update',
  ATTENDANCE_NOTIFICATION: 'attendance_notification',
  EXAM_RESULTS: 'exam_results',
  GENERAL_ANNOUNCEMENT: 'general_announcement',
  WELCOME_MESSAGE: 'welcome_message'
}

// Export singleton instance
export const whatsappService = new WhatsAppService()
